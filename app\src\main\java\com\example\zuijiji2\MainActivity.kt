package com.example.zuijiji2

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import com.example.zuijiji2.ui.theme.Zuijiji2Theme
import kotlin.math.sqrt

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Zuijiji2Theme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var isMagnifierVisible by remember { mutableStateOf(false) }
    var magnifierPosition by remember { mutableStateOf(Offset(200f, 200f)) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(uri),
                contentDescription = "Background Image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 放大镜
        if (isMagnifierVisible && backgroundImageUri != null) {
            MagnifierView(
                backgroundImageUri = backgroundImageUri!!,
                position = magnifierPosition,
                onPositionChange = { magnifierPosition = it }
            )
        }

        // 左下角导入图片按钮
        FloatingActionButton(
            onClick = { imagePickerLauncher.launch("image/*") },
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(16.dp)
        ) {
            Icon(Icons.Default.Add, contentDescription = "导入图片")
        }

        // 右下角放大镜按钮
        FloatingActionButton(
            onClick = { isMagnifierVisible = !isMagnifierVisible },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp)
        ) {
            Icon(Icons.Default.Search, contentDescription = "放大镜")
        }
    }
}

@Composable
fun MagnifierView(
    backgroundImageUri: Uri,
    position: Offset,
    onPositionChange: (Offset) -> Unit
) {
    val density = LocalDensity.current
    val magnifierSizeDp = 80.dp
    val magnificationFactor = 8f

    Box(
        modifier = Modifier
            .size(magnifierSizeDp)
            .offset(
                x = with(density) { (position.x - magnifierSizeDp.toPx() / 2).toDp() },
                y = with(density) { (position.y - magnifierSizeDp.toPx() / 2).toDp() }
            )
            .pointerInput(Unit) {
                detectDragGestures { change, _ ->
                    onPositionChange(position + change)
                }
            }
    ) {
        // 放大的背景图片
        Image(
            painter = rememberAsyncImagePainter(backgroundImageUri),
            contentDescription = "Magnified Image",
            modifier = Modifier
                .size(magnifierSizeDp * magnificationFactor)
                .offset(
                    x = -with(density) { (position.x * magnificationFactor - magnifierSizeDp.toPx() * magnificationFactor / 2).toDp() },
                    y = -with(density) { (position.y * magnificationFactor - magnifierSizeDp.toPx() * magnificationFactor / 2).toDp() }
                )
                .clip(CircleShape),
            contentScale = ContentScale.Crop
        )

        // 放大镜边框
        Box(
            modifier = Modifier
                .size(magnifierSizeDp)
                .clip(CircleShape)
                .background(Color.Transparent)
        ) {
            Canvas(modifier = Modifier.fillMaxSize()) {
                drawCircle(
                    color = Color.Black,
                    radius = size.width / 2,
                    center = Offset(size.width / 2, size.height / 2),
                    style = androidx.compose.ui.graphics.drawscope.Stroke(width = 3.dp.toPx())
                )

                // 添加半透明的边框效果
                drawCircle(
                    color = Color.White.copy(alpha = 0.3f),
                    radius = size.width / 2 - 1.5.dp.toPx(),
                    center = Offset(size.width / 2, size.height / 2),
                    style = androidx.compose.ui.graphics.drawscope.Stroke(width = 1.dp.toPx())
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    Zuijiji2Theme {
        MainScreen()
    }
}